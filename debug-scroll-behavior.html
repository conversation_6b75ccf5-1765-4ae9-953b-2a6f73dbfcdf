<!DOCTYPE html>
<html>
<head>
    <title>滚动行为调试</title>
    <style>
        body {
            height: 200vh;
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 20px;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .back-to-top.animating {
            animation: rotateAndFade 0.6s ease-in-out forwards;
            pointer-events: none;
        }
        
        @keyframes rotateAndFade {
            0% {
                transform: rotate(0deg) scale(1);
                opacity: 1;
            }
            50% {
                transform: rotate(180deg) scale(1.1);
                opacity: 0.8;
            }
            100% {
                transform: rotate(180deg) scale(0.8);
                opacity: 0;
            }
        }
        
        .log {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 300px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="log" id="log"></div>
    
    <h1>滚动行为测试</h1>
    <p>滚动到底部，然后点击返回顶部按钮</p>
    
    <div style="height: 100px; background: #f0f0f0; margin: 20px 0;">内容区域1</div>
    <div style="height: 100px; background: #e0e0e0; margin: 20px 0;">内容区域2</div>
    <div style="height: 100px; background: #d0d0d0; margin: 20px 0;">内容区域3</div>
    
    <button class="back-to-top" id="backToTop" style="display: none;">↑</button>
    
    <script>
        const button = document.getElementById('backToTop');
        const logDiv = document.getElementById('log');
        
        let showBackTop = false;
        let isScrollingToTop = false;
        let backTopAnimating = false;
        let isWaitingForAnimation = false;
        
        function log(message) {
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateButtonVisibility() {
            if (showBackTop) {
                button.style.display = 'block';
            } else {
                button.style.display = 'none';
            }
        }
        
        let ticking = false;
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    const scrollY = window.scrollY;
                    
                    // 正常滚动逻辑
                    if (!isScrollingToTop && !backTopAnimating && !isWaitingForAnimation) {
                        const shouldShow = scrollY > 300;
                        if (showBackTop !== shouldShow) {
                            log(`正常滚动: ${shouldShow}, 位置: ${scrollY}`);
                            showBackTop = shouldShow;
                            updateButtonVisibility();
                        }
                    }
                    
                    // 返回顶部动画触发
                    if (isScrollingToTop && !backTopAnimating && !isWaitingForAnimation && scrollY <= 5) {
                        log(`接近顶部: ${scrollY}, 开始等待`);
                        isWaitingForAnimation = true;
                        
                        setTimeout(() => {
                            const finalY = window.scrollY;
                            log(`延迟检查: ${finalY}`);
                            
                            if (finalY <= 5 && isScrollingToTop && isWaitingForAnimation) {
                                log('开始动画');
                                backTopAnimating = true;
                                button.classList.add('animating');
                                
                                setTimeout(() => {
                                    log('动画完成，隐藏按钮');
                                    showBackTop = false;
                                    backTopAnimating = false;
                                    isScrollingToTop = false;
                                    isWaitingForAnimation = false;
                                    button.classList.remove('animating');
                                    updateButtonVisibility();
                                }, 600);
                            } else {
                                log('检查失败，重置');
                                isWaitingForAnimation = false;
                            }
                        }, 800);
                    }
                    
                    // 手动滚动重置
                    if (scrollY > 300 && (backTopAnimating || isScrollingToTop || isWaitingForAnimation)) {
                        log('手动滚动离开，重置');
                        backTopAnimating = false;
                        isScrollingToTop = false;
                        isWaitingForAnimation = false;
                        button.classList.remove('animating');
                    }
                    
                    ticking = false;
                });
                ticking = true;
            }
        });
        
        button.addEventListener('click', () => {
            log('=== 点击返回顶部 ===');
            isScrollingToTop = true;
            isWaitingForAnimation = false;
            backTopAnimating = false;
            button.classList.remove('animating');
            
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            log('开始滚动');
        });
    </script>
</body>
</html>
