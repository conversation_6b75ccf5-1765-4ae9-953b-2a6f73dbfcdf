<template>
  <div class="weibo-detail">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="container">
        <button class="back-btn" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </button>
        <h1>微博详情</h1>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <!-- 显示骨架屏作为背景 -->
        <div class="error-skeleton">
          <el-skeleton :rows="5" animated />
        </div>
        
        <!-- 错误信息覆盖层 -->
        <div class="error-overlay">
          <el-alert
            title="加载失败"
            :description="error"
            type="error"
            show-icon
            :closable="false"
          />
          <div class="error-actions">
            <el-button @click="loadData" type="primary">重试</el-button>
            <el-button @click="goBack">返回</el-button>
          </div>
        </div>
      </div>

      <!-- 成功状态 - 微博卡片 -->
      <div v-else-if="card" class="detail-content">
        <weibo-card 
          :card="card" 
          :is-visible="true" 
          :show-actions="true"
          :show-user-profile="true"
        ></weibo-card>

        <!-- 警告信息 -->
        <div class="warning-info">
          <el-alert
            title="隐私提醒"
            description="如果你的浏览器登录了微博，可能会在对方主页留下访客记录！"
            type="warning"
            show-icon
            :closable="false"
          />
        </div>
      </div>

      <!-- 无数据状态 -->
      <div v-else class="empty-container">
        <el-empty description="未找到该微博" />
        <el-button @click="goBack" type="primary">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Link, ArrowLeft } from '@element-plus/icons-vue'
import WeiboCard from '../components/WeiboCard.vue'
import httpClient from '../config/http.js'
import { API_CONFIG, isDevelopment } from '../config/api.js'

export default {
  name: 'WeiboDetail',
  components: {
    WeiboCard,
    Link,
    ArrowLeft
  },
  props: {
    weiboId: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()
    
    const card = ref(null)
    const loading = ref(true)
    const error = ref('')

    const loadData = async () => {
      try {
        loading.value = true
        error.value = ''
        card.value = null
        
        // 获取微博ID，优先使用props，其次使用route.params
        const weiboId = props.weiboId || route.params.weiboId
        
        console.log('加载微博详情，ID:', weiboId)
        console.log('Props weiboId:', props.weiboId)
        console.log('Route params:', route.params)
        
        if (!weiboId) {
          throw new Error('微博ID参数缺失')
        }
        
        // 仿照 single_card.html 的请求逻辑，使用配置的 httpClient
        const response = await httpClient.get(`/data/weibo/ajax/${weiboId}.json`)
        
        console.log('API响应:', response)
        
        if (response.data) {
          card.value = response.data
          console.log('微博详情加载成功:', response.data)
        } else {
          throw new Error('微博数据为空')
        }
      } catch (err) {
        console.error('加载微博详情失败:', err)
        
        // 设置错误信息，但保持 loading 为 false 以显示错误状态
        const errorMessage = err.response?.status === 404 
          ? '微博不存在或已被删除' 
          : err.response?.data?.message || err.message || '加载失败，请重试'
        
        error.value = errorMessage
        
        // 显示错误消息
        ElMessage.error('加载微博详情失败')
      } finally {
        loading.value = false
      }
    }

    const goBack = () => {
      // 如果有来源页面，返回上一页；否则返回首页
      if (window.history.length > 1) {
        router.go(-1)
      } else {
        router.push('/')
      }
    }



    // 监听路由参数变化
    watch(() => route.params.weiboId, (newWeiboId) => {
      console.log('路由参数变化:', newWeiboId)
      if (newWeiboId) {
        loadData()
      }
    }, { immediate: true })

    // 监听props变化
    watch(() => props.weiboId, (newWeiboId) => {
      console.log('Props参数变化:', newWeiboId)
      if (newWeiboId) {
        loadData()
      }
    }, { immediate: true })

    onMounted(() => {
      console.log('WeiboDetail组件已挂载')
      console.log('当前props:', props)
      console.log('当前route.params:', route.params)
      
      // 确保在组件挂载时加载数据
      const weiboId = props.weiboId || route.params.weiboId
      if (weiboId) {
        loadData()
      } else {
        error.value = '微博ID参数错误'
        loading.value = false
      }
    })

    return {
      card,
      loading,
      error,
      loadData,
      goBack
    }
  }
}
</script>

<style lang="scss" scoped>
.weibo-detail {
  min-height: 100vh;
}

.page-header {
  background: $bg-primary;
  border-bottom: 1px solid $border-light;
  padding: $space-lg 0;
  margin-bottom: $space-lg;
  
  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 $space-lg;
    display: flex;
    align-items: center;
    gap: $space-lg;
    
    @media (max-width: #{$breakpoint-mobile - 1px}) {
      padding: 0 $space-md;
      gap: $space-md;
    }
  }
  
  .back-btn {
    @include button-base;
    @include button-secondary;
    padding: $space-sm $space-md;
    font-size: $font-size-sm;
    display: flex;
    align-items: center;
    gap: $space-xs;
    
    @include mobile {
      padding: $space-xs $space-sm;
    }
  }
  
  h1 {
    margin: 0;
    color: $text-primary;
    font-size: $font-size-xl;
    font-weight: 600;
    
    @include mobile {
      font-size: $font-size-lg;
    }
  }
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 $space-lg;
  
  @media (max-width: #{$breakpoint-mobile - 1px}) {
    padding: 0 $space-md;
  }
}

.loading-container {
  padding: $space-2xl 0;
  
  .el-skeleton {
    @include card-base;
    padding: $space-xl;
    margin-bottom: $space-lg;
  }
}

.error-container {
  position: relative;
  padding: $space-2xl 0;
  min-height: 400px;
  
  .error-skeleton {
    opacity: 0.2;
    pointer-events: none;
    
    .el-skeleton {
      @include card-base;
      padding: $space-xl;
      margin-bottom: $space-lg;
    }
  }
  
  .error-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 500px;
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: $space-2xl;
    border-radius: $radius-xl;
    box-shadow: $shadow-hover;
    backdrop-filter: blur(10px);
    z-index: 10;
    
    @include mobile {
      width: 95%;
      padding: $space-xl;
    }
  }
  
  .error-actions {
    margin-top: $space-xl;
    display: flex;
    gap: $space-md;
    justify-content: center;
    
    @include mobile {
      flex-direction: column;
      gap: $space-sm;
    }
  }
}

.detail-content {
  animation: fadeInUp 0.6s ease-out;
  margin-bottom: $space-xl;
}

.warning-info {
  margin-top: $space-xl;
  
  .el-alert {
    border-radius: $radius-lg;
  }
}

.empty-container {
  padding: $space-2xl 0;
  text-align: center;
  
  .el-empty {
    padding: $space-2xl;
  }
  
  .el-button {
    margin-top: $space-lg;
  }
}

// 页面进入动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>