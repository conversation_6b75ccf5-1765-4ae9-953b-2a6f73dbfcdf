<template>
  <div class="user-profile">
    <!-- 智能搜索框 -->
    <SmartSearchBox
      ref="searchBoxRef"
      v-model:keyword="keyword"
      @search="handleSearch"
    />

    <!-- 内容区域 -->
    <div class="container" :style="{ paddingTop: containerPaddingTop + 'px' }">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>

      <!-- 微博卡片 -->
      <template v-else>
        <ul class="infinite-list" style="overflow:visible">
          <li 
            v-for="(card, index) in cards" 
            :key="card.id" 
            class="card-observer infinite-list-item" 
            :data-index="index"
          >
            <weibo-card 
              :card="card" 
              :is-visible="card.isCardVisible"
              :clickable="true"
              @card-click="handleCardClick"
            ></weibo-card>
          </li>
        </ul>

        <!-- 加载更多状态 -->
        <div v-if="loadingMore" class="loading-more">
          <el-skeleton :rows="2" animated />
        </div>

        <div v-if="!nextCursor && cards.length && !loadingMore" class="no-more">
          —— 已显示全部内容 ——
        </div>

        <!-- 测试内容区域 - 确保页面有足够高度测试滚动效果 -->
        <div v-if="!loading && cards.length === 0" class="test-content">
          <div class="test-card" v-for="i in 20" :key="i">
            <h3>测试卡片 {{ i }}</h3>
            <p>这是用于测试搜索框动画效果的内容。滚动页面可以看到搜索框的吸附效果。</p>
            <p>搜索框现在应该与卡片宽度保持一致，并且在顶部附近不会出现抖动。</p>
          </div>
        </div>
      </template>
    </div>

    <!-- 返回顶部 -->
    <button
      class="back-to-top"
      :class="{ 'animating': backTopAnimating }"
      @click="scrollToTop"
      v-show="showBackTop"
    >
      ↑
    </button>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import WeiboCard from '../components/WeiboCard.vue'
import SmartSearchBox from '../components/SmartSearchBox.vue'
import { weiboService } from '../services/weiboService'
import { API_CONFIG, isDevelopment } from '../config/api.js'

export default {
  name: 'UserProfile',
  components: {
    WeiboCard,
    SmartSearchBox
  },
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()

    const searchBoxRef = ref(null)
    const cards = ref([])
    const loading = ref(true)
    const loadingMore = ref(false)
    const autoLoading = ref(false)

    // 计算容器的 padding-top，与搜索框的显示状态同步
    const containerPaddingTop = computed(() => {
      if (searchBoxRef.value) {
        return searchBoxRef.value.firstCardPaddingTop || 80
      }
      return 80 // 默认搜索框高度
    })
    const nextCursor = ref(null)
    const keyword = ref('')
    const showBackTop = ref(false)
    const backTopAnimating = ref(false)
    const isScrollingToTop = ref(false) // 是否正在执行返回顶部操作
    const isWaitingForAnimation = ref(false) // 是否正在等待动画触发
    const searchTimer = ref(null)
    const scrollListener = ref(null)
    const isRequesting = ref(false)
    const lastScrollTop = ref(0)

    const initScrollListener = () => {
      let ticking = false
      let lastLoadTime = 0
      
      scrollListener.value = () => {
        if (!ticking) {
          requestAnimationFrame(() => {
            const currentScrollTop = window.scrollY

            // 正常滚动逻辑：控制按钮显示/隐藏
            if (!isScrollingToTop.value && !backTopAnimating.value && !isWaitingForAnimation.value) {
              const shouldShow = currentScrollTop > 300
              if (showBackTop.value !== shouldShow) {
                console.log(`正常滚动更新按钮显示: ${shouldShow}, 当前位置: ${currentScrollTop}`)
                showBackTop.value = shouldShow
              }
            }

            // 返回顶部后的动画触发逻辑 - 检测滚动停止
            if (isScrollingToTop.value && !backTopAnimating.value && !isWaitingForAnimation.value && currentScrollTop <= 5) {
              console.log(`接近顶部，位置: ${currentScrollTop}，设置延迟检查`)
              isWaitingForAnimation.value = true

              // 延迟检查，确保滚动真正停止
              setTimeout(() => {
                const finalPosition = window.scrollY
                console.log(`延迟检查结果，最终位置: ${finalPosition}`)

                if (finalPosition <= 5 && isScrollingToTop.value && isWaitingForAnimation.value) {
                  console.log('确认滚动停止，开始执行隐藏动画')
                  backTopAnimating.value = true

                  // 600ms后隐藏按钮并重置状态
                  setTimeout(() => {
                    console.log('动画完成，隐藏按钮')
                    showBackTop.value = false
                    backTopAnimating.value = false
                    isScrollingToTop.value = false
                    isWaitingForAnimation.value = false
                  }, 600)
                } else {
                  console.log('滚动检查失败，重置等待状态')
                  isWaitingForAnimation.value = false
                }
              }, 800) // 增加到800ms，确保滚动完全停止
            }

            // 如果用户手动滚动离开顶部区域，重置所有状态
            if (currentScrollTop > 300 && (backTopAnimating.value || isScrollingToTop.value || isWaitingForAnimation.value)) {
              console.log('用户手动滚动离开顶部，重置所有状态')
              backTopAnimating.value = false
              isScrollingToTop.value = false
              isWaitingForAnimation.value = false
            }

            if (loading.value || loadingMore.value || isRequesting.value || !nextCursor.value) {
              lastScrollTop.value = currentScrollTop
              ticking = false
              return
            }

            const { scrollTop, clientHeight, scrollHeight } = document.documentElement
            const scrollPosition = scrollTop + clientHeight
            const distanceFromBottom = scrollHeight - scrollPosition
            
            const scrollDelta = currentScrollTop - lastScrollTop.value
            const isFastScrollDown = scrollDelta > 100

            const shouldLoad = (distanceFromBottom <= 500) || 
                              (distanceFromBottom <= 800 && isFastScrollDown) ||
                              (distanceFromBottom <= 10)
            
            if (shouldLoad && nextCursor.value) {
              const now = Date.now()
              let minInterval = 500
              if (distanceFromBottom <= 10) {
                minInterval = 100
              } else if (isFastScrollDown) {
                minInterval = 200
              }
              
              if (now - lastLoadTime >= minInterval) {
                lastLoadTime = now
                loadMore()
              }
            }
            
            lastScrollTop.value = currentScrollTop
            ticking = false
          })
          ticking = true
        }
      }
      
      window.addEventListener('scroll', scrollListener.value, { passive: true })
    }

    const handleSearch = () => {
      clearTimeout(searchTimer.value)
      searchTimer.value = setTimeout(() => {
        cards.value = []
        nextCursor.value = null
        isRequesting.value = false
        loadingMore.value = false
        autoLoading.value = false
        loading.value = true
        loadData()
      }, 300)
    }

    const loadData = async () => {
      try {
        if (!loadingMore.value) {
          loading.value = true
        }
        
        const params = {
          user: props.userId,
          limit: 5,
          cursor: nextCursor.value,
          keyword: keyword.value
        }

        const data = await weiboService.getTweets(params)

        if (data?.success) {
          const newCards = data.data?.length ? data.data.map(card => ({
            ...card,
            isCardVisible: false
          })) : []

          if (newCards.length > 0) {
            cards.value = cards.value.concat(newCards)
            nextCursor.value = data.next_cursor || null
          } else {
            nextCursor.value = null
            if (autoLoading.value) {
              autoLoading.value = false
              ElMessage.info('没有更多内容了')
            }
          }
        } else {
          nextCursor.value = null
          autoLoading.value = false
          if (loadingMore.value) {
            ElMessage.info('没有更多内容了')
          }
        }
      } catch (error) {
        nextCursor.value = null
        autoLoading.value = false
        console.error('加载错误:', error)
        if (loadingMore.value) {
          ElMessage.error('加载失败，请重试')
        }
      } finally {
        loading.value = false
        loadingMore.value = false
      }
    }

    const loadMore = async () => {
      if (!nextCursor.value || loadingMore.value || isRequesting.value) {
        return
      }
      
      isRequesting.value = true
      loadingMore.value = true
      autoLoading.value = true
      
      try {
        await loadData()
        await nextTick()
      } finally {
        isRequesting.value = false
      }
    }

    const scrollToTop = () => {
      console.log('=== 点击返回顶部 ===')

      // 重置所有状态并标记开始返回顶部操作
      isScrollingToTop.value = true
      isWaitingForAnimation.value = false
      backTopAnimating.value = false
      console.log('设置 isScrollingToTop = true, 重置其他状态')

      // 执行滚动，剩下的交给主滚动监听器处理
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
      console.log('开始滚动到顶部，等待主滚动监听器检测到顶部位置')
    }

    const handleCardClick = (card) => {
      // 跳转到微博详情页
      router.push(`/detail/${card.id}`)
    }

    // 监听路由参数变化
    watch(() => props.userId, (newUserId) => {
      if (newUserId) {
        // 重置状态
        cards.value = []
        nextCursor.value = null
        isRequesting.value = false
        loadingMore.value = false
        autoLoading.value = false
        keyword.value = ''
        loading.value = true
        loadData()
      }
    })

    onMounted(() => {
      if (isDevelopment()) {
        console.log('🌍 当前环境:', API_CONFIG.ENV)
        console.log('🔗 API 地址:', API_CONFIG.BASE_URL)
      }
      
      if (props.userId) {
        console.log('初始化用户页面，用户ID:', props.userId)
        isRequesting.value = false
        loadingMore.value = false
        autoLoading.value = false
        loadData()
        initScrollListener()
      } else {
        ElMessage.error('用户ID参数错误')
        loading.value = false
      }
    })

    onUnmounted(() => {
      if (scrollListener.value) {
        window.removeEventListener('scroll', scrollListener.value, { passive: true })
      }
    })

    return {
      searchBoxRef,
      containerPaddingTop,
      cards,
      loading,
      loadingMore,
      autoLoading,
      nextCursor,
      keyword,
      showBackTop,
      backTopAnimating,
      isScrollingToTop,
      isWaitingForAnimation,
      searchTimer,
      scrollListener,
      isRequesting,
      lastScrollTop,
      handleSearch,
      loadData,
      loadMore,
      scrollToTop,
      handleCardClick
    }
  }
}
</script>

<style lang="scss" scoped>
.user-profile {
  min-height: 100vh;
  // padding-top 现在由 JavaScript 动态控制
}



.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 $space-lg;
  
  @media (max-width: #{$breakpoint-mobile - 1px}) {
    padding: 0 $space-md;
  }
}

.loading-container {
  padding: $space-xl 0;
  
  .el-skeleton {
    @include card-base;
    padding: $space-xl;
    margin-bottom: $space-lg;
  }
}

.infinite-list {
  list-style: none;
  padding: 0;
  margin: $space-lg 0;
  
  &-item {
    margin-bottom: $space-lg;
    animation: fadeInUp 0.6s ease-out;
    
    @include mobile {
      margin-bottom: $space-md;
    }
  }
}

.loading-more {
  padding: $space-xl 0;
  text-align: center;
  
  .el-skeleton {
    @include card-base;
    padding: $space-xl;
    margin: 0 auto;
  }
}

.no-more {
  text-align: center;
  color: $text-tertiary;
  padding: $space-2xl 0;
  font-size: $font-size-sm;
  position: relative;
  
  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 60px;
    height: 1px;
    background: linear-gradient(90deg, transparent, $border-default, transparent);
  }
  
  &::before {
    left: calc(50% - 120px);
  }
  
  &::after {
    right: calc(50% - 120px);
  }
}

.test-content {
  .test-card {
    @include card-base;
    margin-bottom: $space-lg;
    padding: $space-xl;

    h3 {
      margin: 0 0 $space-md 0;
      color: $text-primary;
      font-size: 18px;
      font-weight: 600;
    }

    p {
      margin: 0 0 $space-sm 0;
      color: $text-secondary;
      line-height: 1.6;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.back-to-top {
  position: fixed;
  bottom: $space-2xl;
  right: $space-2xl;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, $primary-color 0%, $primary-hover 100%);
  color: white;
  border: none;
  cursor: pointer;
  font-size: 20px;
  font-weight: bold;
  box-shadow: $shadow-3;
  transition: all $transition-base;
  z-index: $z-index-fixed;
  @include flex-center;

  &:hover:not(.animating) {
    transform: translateY(-4px) scale(1.05);
    box-shadow: $shadow-hover;
  }

  &:active:not(.animating) {
    transform: translateY(-2px) scale(1.02);
  }

  // 动画状态
  &.animating {
    animation: backTopRotateAndFade 0.6s ease-in-out forwards;
    pointer-events: none; // 防止动画期间重复点击
  }
  
  @include mobile {
    width: 48px;
    height: 48px;
    bottom: $space-lg;
    right: $space-lg;
    font-size: 18px;
  }
}

// 页面进入动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 返回顶部按钮动画
@keyframes backTopRotateAndFade {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: rotate(180deg) scale(0.8);
    opacity: 0;
  }
}
</style>